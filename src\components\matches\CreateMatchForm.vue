<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useFederationsStore } from '@/stores/federations'
import { MatchType, CompetitionLevel, EU_COUNTRIES, type AgendaEntry } from '@/stores/matches'
import { storeToRefs } from 'pinia'
import { api } from '@/api/feathers-client'
import type { MatchData } from '@/api/feathers-client'
import { parseDate, today, getLocalTimeZone } from '@internationalized/date'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

// Configure dayjs plugins
dayjs.extend(utc)
dayjs.extend(timezone)

// UI Components
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { DatePicker, DateTimePicker } from '@/components/ui/date-picker'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CalendarIcon, Plus, Trash2, ChevronLeft, ChevronRight } from 'lucide-vue-next'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const federationsStore = useFederationsStore()

const { activeOrganizer } = storeToRefs(userStore)
const { federations, isLoading: federationsLoading } = storeToRefs(federationsStore)

// Form state
const currentStep = ref(1)
const totalSteps = 6
const isSubmitting = ref(false)
const error = ref<string | null>(null)

// Form data
const formData = ref<Partial<MatchData> & {
  coverImageUrl?: string
  matchType?: string
  registrationEnds?: string
}>({
  name: '',
  isActive: true,
  startDate: '',
  endDate: '',
  coverImageUrl: '',
  description: '',
  matchType: '',
  federationId: undefined,
  address: '',
  city: '',
  country: '',
  postcode: '',
  phone: '',
  email: '',
  judges: [],
  agenda: [],
  licenseRequired: false,
  currency: 'PLN',
  competitionLevel: '',
  international: false,
  withoutLimits: false,
  publishAt: '',
  registrationEnds: '',
  maxPlayersAmount: undefined,
  organizerId: undefined
})

// Judges and agenda items
const judges = ref<string[]>([])
const agendaItems = ref<AgendaEntry[]>([])

// Helper functions for judges
function addJudge() {
  judges.value.push('')
}

function removeJudge(index: number) {
  judges.value.splice(index, 1)
}

// Initialize with one judge and one agenda item if needed
if (judges.value.length === 0) {
  judges.value.push('')
}

// Load federations on mount
federationsStore.getAllFederations()

// Set organizer ID when activeOrganizer changes
watch(activeOrganizer, (organizer) => {
  if (organizer) {
    formData.value.organizerId = organizer.id
  }
}, { immediate: true })

// Auto-set end date when start date changes
watch(() => formData.value.startDate, (newStartDate) => {
  if (newStartDate && !formData.value.endDate) {
    formData.value.endDate = newStartDate
  }
})

// Step validation
const isStepValid = computed(() => {
  switch (currentStep.value) {
    case 1: // Basic Info
      return formData.value.name && formData.value.startDate
    case 2: // Location
      return true // All location fields are optional
    case 3: // Divisions
      return true // Placeholder step
    case 4: // Details
      return true // All detail fields are optional
    case 5: // Agenda
      return true // Agenda is optional
    case 6: // Settings
      return true // All settings have defaults
    default:
      return false
  }
})

const canProceed = computed(() => isStepValid.value)
const canGoBack = computed(() => currentStep.value > 1)
const isLastStep = computed(() => currentStep.value === totalSteps)

// Navigation
function nextStep() {
  if (canProceed.value && currentStep.value < totalSteps) {
    currentStep.value++
  }
}

function previousStep() {
  if (canGoBack.value) {
    currentStep.value--
  }
}

// Date handling is now handled by the DatePicker and DateTimePicker components

// Agenda management
function addAgendaItem() {
  agendaItems.value.push({
    name: '',
    description: '',
    location: '',
    date: '',
    time: '',
    goals: '',
    format: '',
    punctation: [],
    typesOfGoals: '',
    numberOfArrows: '',
    competitionBranch: ''
  })
}

function removeAgendaItem(index: number) {
  agendaItems.value.splice(index, 1)
}

// Form submission
async function submitForm() {
  if (!activeOrganizer.value) {
    error.value = 'No active organizer found'
    return
  }

  isSubmitting.value = true
  error.value = null

  try {
    // Prepare form data
    const matchData: MatchData = {
      ...formData.value,
      organizerId: activeOrganizer.value.id,
      judges: judges.value.filter(j => j.trim()),
      agenda: agendaItems.value.filter(item => item.name || item.description)
    } as MatchData

    // Create the match
    const createdMatch = await api.matches.create(matchData)

    // Show success message and redirect
    router.push({ name: 'matches' })
  } catch (err) {
    console.error('Failed to create match:', err)
    error.value = t('matches.matchCreationFailed')
  } finally {
    isSubmitting.value = false
  }
}

// Step titles
const stepTitles = computed(() => [
  t('matches.basicInfo'),
  t('matches.locationInfo'),
  t('matches.divisionsInfo'),
  t('matches.detailsInfo'),
  t('matches.agendaInfo'),
  t('matches.settingsInfo')
])
</script>

<template>
  <div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold">{{ t('matches.createMatch') }}</h1>
      <p class="text-muted-foreground mt-2">
        {{ t('matches.step') }} {{ currentStep }} {{ t('matches.of') }} {{ totalSteps }}: {{ stepTitles[currentStep - 1] }}
      </p>
    </div>

    <!-- Progress indicator -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-2">
        <span v-for="(title, index) in stepTitles" :key="index" class="text-sm font-medium">
          <Badge
            :variant="currentStep > index + 1 ? 'default' : currentStep === index + 1 ? 'secondary' : 'outline'"
            class="mr-2"
          >
            {{ index + 1 }}
          </Badge>
          <span :class="currentStep === index + 1 ? 'text-primary' : 'text-muted-foreground'">
            {{ title }}
          </span>
        </span>
      </div>
      <div class="w-full bg-muted rounded-full h-2">
        <div
          class="bg-primary h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
        />
      </div>
    </div>

    <!-- Error Alert -->
    <Alert v-if="error" variant="destructive" class="mb-6">
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Form Steps -->
    <Card>
      <CardHeader>
        <CardTitle>{{ stepTitles[currentStep - 1] }}</CardTitle>
        <CardDescription v-if="currentStep === 1">
          {{ t('matches.basicInfo') }} - {{ t('common.required') }}
        </CardDescription>
        <CardDescription v-else>
          {{ stepTitles[currentStep - 1] }} - {{ t('matches.optional') }}
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- Step 1: Basic Information -->
        <div v-if="currentStep === 1" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">{{ t('matches.name') }} *</Label>
              <Input
                id="name"
                v-model="formData.name"
                :placeholder="t('matches.name')"
                required
              />
            </div>
            <div class="space-y-2">
              <Label for="matchType">{{ t('matches.matchType') }}</Label>
              <Select
                :model-value="formData.matchType"
                @update:model-value="formData.matchType = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select match type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="MatchType.THREED">3D</SelectItem>
                  <SelectItem :value="MatchType.FIELD">Field</SelectItem>
                  <SelectItem :value="MatchType.OUTDOOR">Outdoor</SelectItem>
                  <SelectItem :value="MatchType.INDOOR">Indoor</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="startDate">{{ t('matches.startDate') }} *</Label>
              <DatePicker
                v-model="formData.startDate"
                placeholder="Select start date"
              />
            </div>
            <div class="space-y-2">
              <Label for="endDate">{{ t('matches.endDate') }}</Label>
              <DatePicker
                v-model="formData.endDate"
                placeholder="Select end date"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="coverImageUrl">{{ t('matches.coverImageUrl') }}</Label>
            <Input
              id="coverImageUrl"
              v-model="formData.coverImageUrl"
              :placeholder="t('matches.coverImageUrl')"
              type="url"
            />
          </div>

          <div class="space-y-2">
            <Label for="description">{{ t('matches.description') }}</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              :placeholder="t('matches.description')"
              rows="4"
            />
          </div>

          <div class="space-y-2">
            <Label for="federation">{{ t('matches.federation') }}</Label>
            <Select
              :model-value="formData.federationId?.toString()"
              @update:model-value="formData.federationId = $event ? parseInt($event as string) : undefined"
            >
              <SelectTrigger>
                <SelectValue :placeholder="t('matches.selectFederation')" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="federation in federations"
                  :key="federation.id"
                  :value="federation.id.toString()"
                >
                  {{ federation.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- Step 2: Location -->
        <div v-if="currentStep === 2" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="address">{{ t('matches.address') }}</Label>
              <Input
                id="address"
                v-model="formData.address"
                :placeholder="t('matches.address')"
              />
            </div>
            <div class="space-y-2">
              <Label for="city">{{ t('matches.city') }}</Label>
              <Input
                id="city"
                v-model="formData.city"
                :placeholder="t('matches.city')"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="country">{{ t('matches.country') }}</Label>
              <Select
                :model-value="formData.country"
                @update:model-value="formData.country = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="country in EU_COUNTRIES"
                    :key="country.code"
                    :value="country.code"
                  >
                    {{ country.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <Label for="postcode">{{ t('matches.postcode') }}</Label>
              <Input
                id="postcode"
                v-model="formData.postcode"
                :placeholder="t('matches.postcode')"
              />
            </div>
          </div>
        </div>

        <!-- Step 3: Divisions (Placeholder) -->
        <div v-if="currentStep === 3" class="space-y-4">
          <div class="text-center py-8">
            <p class="text-muted-foreground">{{ t('matches.divisionsInfo') }} - Coming soon</p>
            <p class="text-sm text-muted-foreground mt-2">This section will allow you to configure style and age divisions.</p>
          </div>
        </div>

        <!-- Step 4: Details -->
        <div v-if="currentStep === 4" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="phone">{{ t('matches.phone') }}</Label>
              <Input
                id="phone"
                v-model="formData.phone"
                :placeholder="t('matches.phone')"
                type="tel"
              />
            </div>
            <div class="space-y-2">
              <Label for="email">{{ t('matches.email') }}</Label>
              <Input
                id="email"
                v-model="formData.email"
                :placeholder="t('matches.email')"
                type="email"
              />
            </div>
          </div>

          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <Label>{{ t('matches.judges') }}</Label>
              <Button @click="addJudge" variant="outline" size="sm">
                <Plus class="w-4 h-4 mr-2" />
                Add Judge
              </Button>
            </div>

            <div v-if="judges.length === 0" class="text-center py-4">
              <p class="text-muted-foreground">No judges added yet</p>
              <Button @click="addJudge" variant="outline" class="mt-2">
                <Plus class="w-4 h-4 mr-2" />
                Add Judge
              </Button>
            </div>

            <div v-else class="space-y-2">
              <div
                v-for="(judge, index) in judges"
                :key="index"
                class="flex items-center gap-2"
              >
                <Input
                  v-model="judges[index]"
                  :placeholder="`Judge ${index + 1} name`"
                  class="flex-1"
                />
                <Button
                  @click="removeJudge(index)"
                  variant="outline"
                  size="sm"
                  class="text-destructive hover:text-destructive"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 5: Agenda -->
        <div v-if="currentStep === 5" class="space-y-4">
          <div class="flex items-center justify-between">
            <Label>{{ t('matches.agendaInfo') }}</Label>
            <Button @click="addAgendaItem" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-2" />
              {{ t('matches.addAgendaItem') }}
            </Button>
          </div>

          <div v-if="agendaItems.length === 0" class="text-center py-8">
            <p class="text-muted-foreground">No agenda items yet</p>
            <Button @click="addAgendaItem" variant="outline" class="mt-2">
              <Plus class="w-4 h-4 mr-2" />
              {{ t('matches.addAgendaItem') }}
            </Button>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(item, index) in agendaItems"
              :key="index"
              class="p-4 border rounded-lg space-y-3"
            >
              <div class="flex items-center justify-between">
                <h4 class="font-medium">Agenda Item {{ index + 1 }}</h4>
                <Button
                  @click="removeAgendaItem(index)"
                  variant="outline"
                  size="sm"
                  class="text-destructive hover:text-destructive"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="space-y-1">
                  <Label :for="`agenda-name-${index}`">Name *</Label>
                  <Input
                    :id="`agenda-name-${index}`"
                    v-model="item.name"
                    placeholder="Event name"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-location-${index}`">Location</Label>
                  <Input
                    :id="`agenda-location-${index}`"
                    v-model="item.location"
                    placeholder="Event location"
                  />
                </div>
              </div>

              <div class="space-y-1">
                <Label :for="`agenda-description-${index}`">Description</Label>
                <Textarea
                  :id="`agenda-description-${index}`"
                  v-model="item.description"
                  placeholder="Event description"
                  rows="2"
                />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div class="space-y-1">
                  <Label :for="`agenda-date-${index}`">Date</Label>
                  <DatePicker
                    v-model="item.date"
                    placeholder="Select date"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-time-${index}`">Time</Label>
                  <Input
                    :id="`agenda-time-${index}`"
                    v-model="item.time"
                    type="time"
                    placeholder="Event time"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-branch-${index}`">Competition Branch</Label>
                  <Input
                    :id="`agenda-branch-${index}`"
                    v-model="item.competitionBranch"
                    placeholder="Competition branch"
                  />
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div class="space-y-1">
                  <Label :for="`agenda-goals-${index}`">Goals</Label>
                  <Input
                    :id="`agenda-goals-${index}`"
                    v-model="item.goals"
                    placeholder="Number of goals"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-arrows-${index}`">Number of Arrows</Label>
                  <Input
                    :id="`agenda-arrows-${index}`"
                    v-model="item.numberOfArrows"
                    placeholder="Number of arrows"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-types-${index}`">Types of Goals</Label>
                  <Input
                    :id="`agenda-types-${index}`"
                    v-model="item.typesOfGoals"
                    placeholder="Types of goals"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 6: Settings -->
        <div v-if="currentStep === 6" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="currency">{{ t('matches.currency') }}</Label>
              <Select
                :model-value="formData.currency"
                @update:model-value="formData.currency = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PLN">PLN</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="CZK">CZK</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <Label for="competitionLevel">{{ t('matches.competitionLevel') }}</Label>
              <Select
                :model-value="formData.competitionLevel"
                @update:model-value="formData.competitionLevel = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select competition level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="CompetitionLevel.FRIENDLY">Friendly</SelectItem>
                  <SelectItem :value="CompetitionLevel.TOURNAMENT">Tournament</SelectItem>
                  <SelectItem :value="CompetitionLevel.NATIONAL">National</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="maxPlayersAmount">{{ t('matches.maxPlayersAmount') }}</Label>
              <Input
                id="maxPlayersAmount"
                v-model="formData.maxPlayersAmount"
                type="number"
                min="1"
                :placeholder="t('matches.maxPlayersAmount')"
              />
            </div>
            <div class="space-y-2">
              <Label for="registrationEnds">{{ t('matches.registrationEnds') }}</Label>
              <DateTimePicker
                v-model="formData.registrationEnds"
                placeholder="Select registration end date and time"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="publishAt">{{ t('matches.publishAt') }}</Label>
            <DateTimePicker
              v-model="formData.publishAt"
              placeholder="Select publish date and time"
            />
          </div>

          <Separator />

          <div class="space-y-4">
            <div class="flex items-center space-x-2">
              <Checkbox
                id="licenseRequired"
                :checked="formData.licenseRequired"
                @update:checked="formData.licenseRequired = $event"
              />
              <Label for="licenseRequired">{{ t('matches.licenseRequired') }}</Label>
            </div>

            <div class="flex items-center space-x-2">
              <Checkbox
                id="international"
                :checked="formData.international"
                @update:checked="formData.international = $event"
              />
              <Label for="international">{{ t('matches.international') }}</Label>
            </div>

            <div class="flex items-center space-x-2">
              <Checkbox
                id="withoutLimits"
                :checked="formData.withoutLimits"
                @update:checked="formData.withoutLimits = $event"
              />
              <Label for="withoutLimits">{{ t('matches.withoutLimits') }}</Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Navigation -->
    <div class="flex justify-between mt-8">
      <Button
        @click="previousStep"
        variant="outline"
        :disabled="!canGoBack"
      >
        <ChevronLeft class="w-4 h-4 mr-2" />
        {{ t('common.previous') }}
      </Button>

      <div class="flex gap-2">
        <Button @click="router.push({ name: 'matches' })" variant="ghost">
          {{ t('common.cancel') }}
        </Button>

        <Button
          v-if="!isLastStep"
          @click="nextStep"
          :disabled="!canProceed"
        >
          {{ t('common.next') }}
          <ChevronRight class="w-4 h-4 ml-2" />
        </Button>

        <Button
          v-else
          @click="submitForm"
          :disabled="!canProceed || isSubmitting"
        >
          {{ isSubmitting ? t('common.loading') : t('common.save') }}
        </Button>
      </div>
    </div>
  </div>
</template>
