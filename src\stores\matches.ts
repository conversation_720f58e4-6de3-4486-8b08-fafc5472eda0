import type { Params } from '@feathersjs/feathers'

import type { Match, MatchData, MatchPatch, MatchRegistration, MatchRegistrationData, MatchRegistrationPatch } from '../api/feathers-client'
import { api } from '../api/feathers-client'

// Match Type Enum
export enum MatchType {
  THREED = '3d',
  FIELD = 'field',
  OUTDOOR = 'outdoor',
  INDOOR = 'indoor'
}

// Competition Level Enum
export enum CompetitionLevel {
  FRIENDLY = 'FRIENDLY',
  TOURNAMENT = 'TOURNAMENT',
  NATIONAL = 'NATIONAL'
}

// EU Countries with codes
export const EU_COUNTRIES = [
  { name: 'Austria', code: 'AT' },
  { name: 'Belgium', code: 'BE' },
  { name: 'Bulgaria', code: 'BG' },
  { name: 'Croatia', code: 'HR' },
  { name: 'Cyprus', code: 'CY' },
  { name: 'Czech Republic', code: 'CZ' },
  { name: 'Denmark', code: 'DK' },
  { name: 'Estonia', code: 'EE' },
  { name: 'Finland', code: 'FI' },
  { name: 'France', code: 'FR' },
  { name: 'Germany', code: 'DE' },
  { name: 'Greece', code: 'GR' },
  { name: 'Hungary', code: 'HU' },
  { name: 'Ireland', code: 'IE' },
  { name: 'Italy', code: 'IT' },
  { name: 'Latvia', code: 'LV' },
  { name: 'Lithuania', code: 'LT' },
  { name: 'Luxembourg', code: 'LU' },
  { name: 'Malta', code: 'MT' },
  { name: 'Netherlands', code: 'NL' },
  { name: 'Poland', code: 'PL' },
  { name: 'Portugal', code: 'PT' },
  { name: 'Romania', code: 'RO' },
  { name: 'Slovakia', code: 'SK' },
  { name: 'Slovenia', code: 'SI' },
  { name: 'Spain', code: 'ES' },
  { name: 'Sweden', code: 'SE' }
] as const

// Service-only matches store - no global state, only API methods
export const useMatchesService = () => {
  // Use the typed service from the api client
  const matchesService = api.matches
  const matchRegistrationsService = api.matchRegistrations

  // Match API methods - no state management, just API calls
  async function findMatches(params?: Params) {
    try {
      const result = await matchesService.find(params)
      if (Array.isArray(result.data)) {
        return result.data
      } else {
        return result as unknown as Match[]
      }
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to fetch matches.')
      }
    }
  }

  async function getMatch(id: number) {
    try {
      const result = await matchesService.get(id)
      return result
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to fetch match with id ${id}.`)
      }
    }
  }

  async function createMatch(data: MatchData) {
    try {
      const newMatch = await matchesService.create(data)
      return newMatch
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to create match.')
      }
    }
  }

  async function patchMatch(id: number, data: MatchPatch) {
    try {
      const updatedMatch = await matchesService.patch(id, data)
      return updatedMatch
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to update match with id ${id}.`)
      }
    }
  }

  async function removeMatch(id: number) {
    try {
      const removedMatch = await matchesService.remove(id)
      return removedMatch
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to remove match with id ${id}.`)
      }
    }
  }

  // Match Registration API methods - no state management, just API calls
  async function findMatchRegistrations(params?: Params) {
    try {
      const result = await matchRegistrationsService.find(params)
      if (Array.isArray(result.data)) {
        return result.data
      } else {
        return result as unknown as MatchRegistration[]
      }
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to fetch match registrations.')
      }
    }
  }

  async function createMatchRegistration(data: MatchRegistrationData) {
    try {
      const newRegistration = await matchRegistrationsService.create(data)
      return newRegistration
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to create match registration.')
      }
    }
  }

  async function patchMatchRegistration(registrationId: number, data: MatchRegistrationPatch) {
    try {
      const updatedRegistration = await matchRegistrationsService.patch(registrationId, data)
      return updatedRegistration
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to update match registration with id ${registrationId}.`)
      }
    }
  }

  async function removeMatchRegistration(registrationId: number) {
    try {
      const removedRegistration = await matchRegistrationsService.remove(registrationId)
      return removedRegistration
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to remove match registration with id ${registrationId}.`)
      }
    }
  }

  return {
    // Match API methods
    findMatches,
    getMatch,
    createMatch,
    patchMatch,
    removeMatch,
    // Match Registration API methods
    findMatchRegistrations,
    createMatchRegistration,
    patchMatchRegistration,
    removeMatchRegistration,
  }
}

// Agenda entry type based on the actual schema
export type AgendaEntry = {
  name: string
  description?: string
  location?: string
  goals?: string | number
  format?: string | number
  punctation?: (string | number)[]
  typesOfGoals?: string
  numberOfArrows?: string | number
  competitionBranch?: string
  date?: string
  time?: string
}
